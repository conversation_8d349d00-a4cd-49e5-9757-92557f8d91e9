"""
Utility functions for Arena Doviz Exchange Accounting System.
"""

import logging
import traceback
from functools import wraps
from typing import Any, Dict, Optional, Union
from decimal import Decimal, InvalidOperation
from django.http import JsonResponse
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from django.conf import settings

logger = logging.getLogger(__name__)


class ArenaDovizException(Exception):
    """Base exception class for Arena Doviz specific errors."""
    
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or 'ARENA_DOVIZ_ERROR'
        self.details = details or {}
        super().__init__(self.message)


class TransactionError(ArenaDovizException):
    """Exception for transaction-related errors."""
    pass


class BalanceError(ArenaDovizException):
    """Exception for balance-related errors."""
    pass


class ExchangeRateError(ArenaDovizException):
    """Exception for exchange rate-related errors."""
    pass


class CustomerError(ArenaDovizException):
    """Exception for customer-related errors."""
    pass


def handle_exceptions(fallback_value=None, log_error=True):
    """
    Decorator to handle exceptions with fallback mechanisms.
    
    Args:
        fallback_value: Value to return if an exception occurs
        log_error: Whether to log the error
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_error:
                    logger.error(
                        f"Error in {func.__name__}: {str(e)}",
                        extra={
                            'function': func.__name__,
                            'args': str(args)[:200],  # Limit args length
                            'kwargs': str(kwargs)[:200],
                            'traceback': traceback.format_exc()
                        }
                    )
                
                # Re-raise Arena Doviz specific exceptions
                if isinstance(e, ArenaDovizException):
                    raise
                
                # Return fallback value for other exceptions
                return fallback_value
        return wrapper
    return decorator


def log_user_action(user, action: str, model_name: str = '', object_id: str = '', 
                   object_repr: str = '', changes: Dict = None, 
                   ip_address: str = None, additional_data: Dict = None):
    """
    Log user actions for audit trail.
    
    Args:
        user: User performing the action
        action: Type of action (create, update, delete, etc.)
        model_name: Name of the model affected
        object_id: ID of the object affected
        object_repr: String representation of the object
        changes: Dictionary of changes made
        ip_address: IP address of the user
        additional_data: Additional context data
    """
    try:
        from apps.accounts.models import AuditLog
        
        AuditLog.log_action(
            user=user,
            action=action,
            model_name=model_name,
            object_id=object_id,
            object_repr=object_repr,
            changes=changes or {},
            ip_address=ip_address,
            additional_data=additional_data or {}
        )
        
        logger.info(
            f"User action logged: {user.username if user else 'Anonymous'} - {action} - {model_name}",
            extra={
                'user_id': user.id if user else None,
                'action': action,
                'model_name': model_name,
                'object_id': object_id,
                'ip_address': ip_address
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to log user action: {e}")


def safe_decimal_conversion(value: Any, default: Decimal = Decimal('0')) -> Decimal:
    """
    Safely convert a value to Decimal with fallback.
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Decimal value or default
    """
    if value is None:
        return default
    
    if isinstance(value, Decimal):
        return value
    
    try:
        return Decimal(str(value))
    except (InvalidOperation, ValueError, TypeError) as e:
        logger.warning(f"Failed to convert {value} to Decimal: {e}")
        return default


def format_currency(amount: Union[Decimal, float, int], currency_code: str, 
                   include_symbol: bool = True) -> str:
    """
    Format currency amount with proper symbol and precision.
    
    Args:
        amount: Amount to format
        currency_code: Currency code (USD, AED, IRR)
        include_symbol: Whether to include currency symbol
        
    Returns:
        Formatted currency string
    """
    try:
        amount_decimal = safe_decimal_conversion(amount)
        
        # Currency symbols and precision
        currency_config = {
            'USD': {'symbol': '$', 'precision': 2},
            'AED': {'symbol': 'د.إ', 'precision': 2},
            'IRR': {'symbol': '﷼', 'precision': 0},
        }
        
        config = currency_config.get(currency_code, {'symbol': currency_code, 'precision': 2})
        precision = config['precision']
        
        # Format with appropriate precision
        if precision == 0:
            formatted = f"{int(amount_decimal):,}"
        else:
            formatted = f"{amount_decimal:,.{precision}f}"
        
        if include_symbol:
            return f"{config['symbol']} {formatted}"
        else:
            return formatted
            
    except Exception as e:
        logger.error(f"Error formatting currency {amount} {currency_code}: {e}")
        return str(amount)


def validate_transaction_data(data: Dict) -> Dict:
    """
    Validate transaction data with comprehensive checks.
    
    Args:
        data: Transaction data dictionary
        
    Returns:
        Validated and cleaned data
        
    Raises:
        ValidationError: If validation fails
    """
    errors = {}
    
    # Required fields
    required_fields = ['customer', 'from_currency', 'to_currency', 'from_amount', 'to_amount']
    for field in required_fields:
        if field not in data or data[field] is None:
            errors[field] = f"{field} is required"
    
    # Amount validation
    try:
        from_amount = safe_decimal_conversion(data.get('from_amount'))
        if from_amount <= 0:
            errors['from_amount'] = "From amount must be positive"
    except Exception:
        errors['from_amount'] = "Invalid from amount"
    
    try:
        to_amount = safe_decimal_conversion(data.get('to_amount'))
        if to_amount <= 0:
            errors['to_amount'] = "To amount must be positive"
    except Exception:
        errors['to_amount'] = "Invalid to amount"
    
    # Exchange rate validation
    if 'exchange_rate' in data:
        try:
            exchange_rate = safe_decimal_conversion(data['exchange_rate'])
            if exchange_rate <= 0:
                errors['exchange_rate'] = "Exchange rate must be positive"
        except Exception:
            errors['exchange_rate'] = "Invalid exchange rate"
    
    if errors:
        raise ValidationError(errors)
    
    return data


def calculate_exchange_rate(from_amount: Decimal, to_amount: Decimal) -> Decimal:
    """
    Calculate exchange rate from amounts.
    
    Args:
        from_amount: Amount in source currency
        to_amount: Amount in target currency
        
    Returns:
        Exchange rate
        
    Raises:
        ExchangeRateError: If calculation fails
    """
    try:
        from_amount = safe_decimal_conversion(from_amount)
        to_amount = safe_decimal_conversion(to_amount)
        
        if from_amount <= 0:
            raise ExchangeRateError("From amount must be positive")
        
        rate = to_amount / from_amount
        return rate
        
    except Exception as e:
        logger.error(f"Error calculating exchange rate: {e}")
        raise ExchangeRateError(f"Failed to calculate exchange rate: {e}")


def get_client_ip(request) -> str:
    """
    Get client IP address from request.
    
    Args:
        request: Django request object
        
    Returns:
        Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', 'unknown')
    
    return ip


def create_error_response(message: str, error_code: str = None, 
                         status_code: int = 400, details: Dict = None) -> JsonResponse:
    """
    Create standardized error response.
    
    Args:
        message: Error message
        error_code: Error code
        status_code: HTTP status code
        details: Additional error details
        
    Returns:
        JsonResponse with error information
    """
    response_data = {
        'error': True,
        'message': message,
        'error_code': error_code or 'GENERIC_ERROR',
        'timestamp': timezone.now().isoformat()
    }
    
    if details:
        response_data['details'] = details
    
    return JsonResponse(response_data, status=status_code)


def create_success_response(data: Any = None, message: str = 'Success') -> JsonResponse:
    """
    Create standardized success response.
    
    Args:
        data: Response data
        message: Success message
        
    Returns:
        JsonResponse with success information
    """
    response_data = {
        'success': True,
        'message': message,
        'timestamp': timezone.now().isoformat()
    }
    
    if data is not None:
        response_data['data'] = data
    
    return JsonResponse(response_data)


@transaction.atomic
def safe_database_operation(operation_func, *args, **kwargs):
    """
    Execute database operation safely with transaction rollback on error.
    
    Args:
        operation_func: Function to execute
        *args: Arguments for the function
        **kwargs: Keyword arguments for the function
        
    Returns:
        Result of the operation
        
    Raises:
        Exception: Re-raises the original exception after logging
    """
    try:
        logger.debug(f"Starting database operation: {operation_func.__name__}")
        result = operation_func(*args, **kwargs)
        logger.debug(f"Database operation completed successfully: {operation_func.__name__}")
        return result
        
    except Exception as e:
        logger.error(
            f"Database operation failed: {operation_func.__name__} - {str(e)}",
            extra={
                'function': operation_func.__name__,
                'args': str(args)[:200],
                'kwargs': str(kwargs)[:200],
                'traceback': traceback.format_exc()
            }
        )
        raise


def mask_sensitive_data(data: Dict, sensitive_fields: list = None) -> Dict:
    """
    Mask sensitive data in dictionary for logging.
    
    Args:
        data: Dictionary containing data
        sensitive_fields: List of field names to mask
        
    Returns:
        Dictionary with sensitive fields masked
    """
    if sensitive_fields is None:
        sensitive_fields = ['password', 'token', 'secret', 'key', 'pin']
    
    masked_data = data.copy()
    
    for field in sensitive_fields:
        if field in masked_data:
            masked_data[field] = '***MASKED***'
    
    return masked_data


def setup_logging():
    """
    Setup comprehensive logging configuration.
    """
    import os
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(settings.BASE_DIR, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure loggers
    loggers = {
        'apps.accounts': logging.INFO,
        'apps.customers': logging.INFO,
        'apps.transactions': logging.INFO,
        'apps.currencies': logging.INFO,
        'apps.locations': logging.INFO,
        'apps.reports': logging.INFO,
        'apps.core': logging.INFO,
    }
    
    for logger_name, level in loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
        
        # Add file handler if not already present
        if not logger.handlers:
            handler = logging.FileHandler(
                os.path.join(log_dir, f'{logger_name.replace(".", "_")}.log')
            )
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)


# Initialize logging when module is imported
setup_logging()
