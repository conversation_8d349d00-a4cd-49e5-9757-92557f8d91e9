<!DOCTYPE html>
<html lang="{% load i18n %}{% get_current_language as LANGUAGE_CODE %}{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'fa' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Arena Doviz - Exchange Accounting System{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{% load static %}{% static 'css/arena-doviz.css' %}" rel="stylesheet">
    
    {% if LANGUAGE_CODE == 'fa' %}
    <!-- RTL Support for Persian -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="{% load static %}{% static 'css/rtl.css' %}" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'core_web:dashboard' %}">
                <i class="bi bi-currency-exchange"></i>
                Arena Doviz
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'core_web:dashboard' %}">
                            <i class="bi bi-speedometer2"></i>
                            {% trans "Dashboard" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'customers_web:list' %}">
                            <i class="bi bi-people"></i>
                            {% trans "Customers" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'transactions_web:list' %}">
                            <i class="bi bi-arrow-left-right"></i>
                            {% trans "Transactions" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reports_web:list' %}">
                            <i class="bi bi-file-earmark-text"></i>
                            {% trans "Reports" %}
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ user.get_display_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts_web:profile' %}">
                                <i class="bi bi-person"></i> {% trans "Profile" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts_web:logout' %}">
                                <i class="bi bi-box-arrow-right"></i> {% trans "Logout" %}
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts_web:login' %}">
                            <i class="bi bi-box-arrow-in-right"></i>
                            {% trans "Login" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; {% now "Y" %} Arena Doviz Exchange Accounting System
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 text-muted">
                        {% trans "Version" %} 1.0.0
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="{% load static %}{% static 'js/arena-doviz.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
