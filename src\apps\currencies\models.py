"""
Currency and exchange rate models for Arena Doviz Exchange Accounting System.
Handles USD, AED, and IRR currencies with location-specific exchange rates.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from apps.core.models import BaseModel, TimeStampedModel
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class Currency(BaseModel):
    """
    Model representing currencies supported by the exchange system.
    Supports USD, AED, and IRR as per business requirements.
    """
    
    code = models.CharField(
        _('Currency code'),
        max_length=3,
        unique=True,
        help_text=_('ISO 4217 currency code (e.g., USD, AED, IRR)')
    )
    
    name = models.CharField(
        _('Currency name'),
        max_length=100,
        help_text=_('Full name of the currency (e.g., US Dollar)')
    )
    
    symbol = models.Char<PERSON>ield(
        _('Currency symbol'),
        max_length=10,
        help_text=_('Currency symbol (e.g., $, د.إ, ﷼)')
    )
    
    decimal_places = models.PositiveSmallIntegerField(
        _('Decimal places'),
        default=2,
        validators=[MaxValueValidator(4)],
        help_text=_('Number of decimal places for this currency')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this currency is currently active')
    )
    
    is_base_currency = models.BooleanField(
        _('Is base currency'),
        default=False,
        help_text=_('Whether this is the base currency for exchange rate calculations')
    )
    
    sort_order = models.PositiveIntegerField(
        _('Sort order'),
        default=0,
        help_text=_('Order in which currencies should be displayed')
    )
    
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this currency')
    )
    
    class Meta:
        verbose_name = _('Currency')
        verbose_name_plural = _('Currencies')
        ordering = ['sort_order', 'code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_base_currency']),
            models.Index(fields=['sort_order']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['is_base_currency'],
                condition=models.Q(is_base_currency=True),
                name='unique_base_currency'
            )
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_display_name(self):
        """Return a display name with symbol."""
        return f"{self.name} ({self.symbol})"
    
    def format_amount(self, amount):
        """Format an amount according to currency decimal places."""
        if amount is None:
            return "0"
        
        if self.decimal_places == 0:
            return f"{int(amount):,}"
        else:
            format_str = f"{{:,.{self.decimal_places}f}}"
            return format_str.format(float(amount))
    
    def format_amount_with_symbol(self, amount):
        """Format an amount with currency symbol."""
        formatted_amount = self.format_amount(amount)
        return f"{self.symbol} {formatted_amount}"
    
    def clean(self):
        """Validate currency data."""
        super().clean()
        
        # Validate currency code format
        if not self.code.isupper() or len(self.code) != 3:
            raise ValidationError({
                'code': _('Currency code must be exactly 3 uppercase letters')
            })
    
    def save(self, *args, **kwargs):
        """
        Override save to handle business logic.
        """
        # Ensure only one base currency
        if self.is_base_currency:
            Currency.objects.filter(is_base_currency=True).exclude(pk=self.pk).update(is_base_currency=False)
        
        # Convert code to uppercase
        self.code = self.code.upper()
        
        super().save(*args, **kwargs)
        
        logger.info(f"Currency saved: {self.code} - {self.name}")
    
    @classmethod
    def get_base_currency(cls):
        """Get the base currency."""
        try:
            return cls.objects.get(is_base_currency=True, is_active=True, is_deleted=False)
        except cls.DoesNotExist:
            logger.warning("No base currency found")
            return None
        except cls.MultipleObjectsReturned:
            logger.error("Multiple base currencies found")
            return cls.objects.filter(is_base_currency=True, is_active=True, is_deleted=False).first()
    
    @classmethod
    def get_active_currencies(cls):
        """Get all active currencies."""
        return cls.objects.filter(is_active=True, is_deleted=False).order_by('sort_order', 'code')


class ExchangeRate(BaseModel):
    """
    Model for storing exchange rates between currencies.
    Supports location-specific rates as per business requirements.
    """
    
    from_currency = models.ForeignKey(
        Currency,
        on_delete=models.PROTECT,
        related_name='rates_from',
        verbose_name=_('From currency'),
        help_text=_('Currency being exchanged from')
    )
    
    to_currency = models.ForeignKey(
        Currency,
        on_delete=models.PROTECT,
        related_name='rates_to',
        verbose_name=_('To currency'),
        help_text=_('Currency being exchanged to')
    )
    
    location = models.ForeignKey(
        'locations.Location',
        on_delete=models.PROTECT,
        related_name='exchange_rates',
        verbose_name=_('Location'),
        help_text=_('Location where this exchange rate applies')
    )
    
    # Buy and sell rates
    buy_rate = models.DecimalField(
        _('Buy rate'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))],
        help_text=_('Rate at which we buy the from_currency')
    )
    
    sell_rate = models.DecimalField(
        _('Sell rate'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))],
        help_text=_('Rate at which we sell the from_currency')
    )
    
    # Rate metadata
    effective_from = models.DateTimeField(
        _('Effective from'),
        help_text=_('Date and time when this rate becomes effective')
    )
    
    effective_until = models.DateTimeField(
        _('Effective until'),
        null=True,
        blank=True,
        help_text=_('Date and time when this rate expires (null for current rate)')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this exchange rate is currently active')
    )
    
    # Source and notes
    source = models.CharField(
        _('Rate source'),
        max_length=100,
        blank=True,
        help_text=_('Source of the exchange rate (e.g., Central Bank, Market)')
    )
    
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this exchange rate')
    )
    
    class Meta:
        verbose_name = _('Exchange Rate')
        verbose_name_plural = _('Exchange Rates')
        ordering = ['-effective_from']
        indexes = [
            models.Index(fields=['from_currency', 'to_currency', 'location']),
            models.Index(fields=['effective_from']),
            models.Index(fields=['is_active']),
            models.Index(fields=['location']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['from_currency', 'to_currency', 'location', 'effective_from'],
                name='unique_rate_per_location_time'
            ),
            models.CheckConstraint(
                check=models.Q(buy_rate__gt=0) & models.Q(sell_rate__gt=0),
                name='positive_rates'
            ),
        ]
    
    def __str__(self):
        return f"{self.from_currency.code}/{self.to_currency.code} @ {self.location.code} - Buy: {self.buy_rate}, Sell: {self.sell_rate}"
    
    def get_spread(self):
        """Calculate the spread between buy and sell rates."""
        return self.sell_rate - self.buy_rate
    
    def get_spread_percentage(self):
        """Calculate the spread as a percentage of the buy rate."""
        if self.buy_rate > 0:
            return (self.get_spread() / self.buy_rate) * 100
        return 0
    
    def is_current(self):
        """Check if this rate is currently effective."""
        from django.utils import timezone
        now = timezone.now()
        
        if not self.is_active:
            return False
        
        if self.effective_from > now:
            return False
        
        if self.effective_until and self.effective_until <= now:
            return False
        
        return True
    
    def clean(self):
        """Validate exchange rate data."""
        super().clean()
        
        # Validate that from_currency and to_currency are different
        if self.from_currency == self.to_currency:
            raise ValidationError({
                'to_currency': _('From currency and to currency must be different')
            })
        
        # Validate that sell rate is higher than buy rate
        if self.sell_rate <= self.buy_rate:
            raise ValidationError({
                'sell_rate': _('Sell rate must be higher than buy rate')
            })
        
        # Validate effective dates
        if self.effective_until and self.effective_until <= self.effective_from:
            raise ValidationError({
                'effective_until': _('Effective until date must be after effective from date')
            })
    
    def save(self, *args, **kwargs):
        """
        Override save to handle business logic.
        """
        # Set effective_from to now if not provided
        if not self.effective_from:
            from django.utils import timezone
            self.effective_from = timezone.now()
        
        super().save(*args, **kwargs)
        
        logger.info(f"Exchange rate saved: {self.from_currency.code}/{self.to_currency.code} @ {self.location.code}")
    
    @classmethod
    def get_current_rate(cls, from_currency, to_currency, location, rate_type='sell'):
        """
        Get the current exchange rate for a currency pair at a location.
        
        Args:
            from_currency: Currency object or code
            to_currency: Currency object or code
            location: Location object or code
            rate_type: 'buy' or 'sell'
        
        Returns:
            Decimal: Current exchange rate or None if not found
        """
        from django.utils import timezone
        
        try:
            # Handle currency codes
            if isinstance(from_currency, str):
                from_currency = Currency.objects.get(code=from_currency, is_active=True)
            if isinstance(to_currency, str):
                to_currency = Currency.objects.get(code=to_currency, is_active=True)
            if isinstance(location, str):
                from apps.locations.models import Location
                location = Location.objects.get(code=location, is_active=True)
            
            now = timezone.now()
            
            # Find current rate
            rate = cls.objects.filter(
                from_currency=from_currency,
                to_currency=to_currency,
                location=location,
                is_active=True,
                is_deleted=False,
                effective_from__lte=now
            ).filter(
                models.Q(effective_until__isnull=True) | models.Q(effective_until__gt=now)
            ).order_by('-effective_from').first()
            
            if rate:
                return rate.buy_rate if rate_type == 'buy' else rate.sell_rate
            
            logger.warning(f"No current rate found for {from_currency.code}/{to_currency.code} @ {location.code}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting current rate: {e}")
            return None
    
    @classmethod
    def get_latest_rates_by_location(cls, location):
        """Get the latest exchange rates for all currency pairs at a location."""
        from django.utils import timezone
        
        try:
            if isinstance(location, str):
                from apps.locations.models import Location
                location = Location.objects.get(code=location, is_active=True)
            
            now = timezone.now()
            
            # Get latest rates for each currency pair
            rates = cls.objects.filter(
                location=location,
                is_active=True,
                is_deleted=False,
                effective_from__lte=now
            ).filter(
                models.Q(effective_until__isnull=True) | models.Q(effective_until__gt=now)
            ).order_by('from_currency', 'to_currency', '-effective_from').distinct(
                'from_currency', 'to_currency'
            )
            
            return rates
            
        except Exception as e:
            logger.error(f"Error getting latest rates for location: {e}")
            return cls.objects.none()


class ExchangeRateHistory(TimeStampedModel):
    """
    Model for tracking exchange rate changes for audit and analysis purposes.
    """
    
    exchange_rate = models.ForeignKey(
        ExchangeRate,
        on_delete=models.CASCADE,
        related_name='history',
        verbose_name=_('Exchange rate')
    )
    
    old_buy_rate = models.DecimalField(
        _('Old buy rate'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True
    )
    
    new_buy_rate = models.DecimalField(
        _('New buy rate'),
        max_digits=15,
        decimal_places=6
    )
    
    old_sell_rate = models.DecimalField(
        _('Old sell rate'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True
    )
    
    new_sell_rate = models.DecimalField(
        _('New sell rate'),
        max_digits=15,
        decimal_places=6
    )
    
    changed_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='rate_changes',
        verbose_name=_('Changed by')
    )
    
    reason = models.TextField(
        _('Reason for change'),
        blank=True,
        help_text=_('Reason for the rate change')
    )
    
    class Meta:
        verbose_name = _('Exchange Rate History')
        verbose_name_plural = _('Exchange Rate History')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['exchange_rate', 'created_at']),
            models.Index(fields=['changed_by']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Rate change for {self.exchange_rate} at {self.created_at}"
    
    def get_buy_rate_change(self):
        """Calculate the change in buy rate."""
        if self.old_buy_rate:
            return self.new_buy_rate - self.old_buy_rate
        return self.new_buy_rate
    
    def get_sell_rate_change(self):
        """Calculate the change in sell rate."""
        if self.old_sell_rate:
            return self.new_sell_rate - self.old_sell_rate
        return self.new_sell_rate
    
    def get_buy_rate_change_percentage(self):
        """Calculate the percentage change in buy rate."""
        if self.old_buy_rate and self.old_buy_rate > 0:
            return ((self.new_buy_rate - self.old_buy_rate) / self.old_buy_rate) * 100
        return 0
    
    def get_sell_rate_change_percentage(self):
        """Calculate the percentage change in sell rate."""
        if self.old_sell_rate and self.old_sell_rate > 0:
            return ((self.new_sell_rate - self.old_sell_rate) / self.old_sell_rate) * 100
        return 0
