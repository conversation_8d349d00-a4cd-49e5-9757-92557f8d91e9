"""
URL configuration for Arena Doviz Exchange Accounting System.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
import logging

logger = logging.getLogger(__name__)

# Main URL patterns
urlpatterns = [
    # Admin interface
    path('admin/', admin.site.urls),
    
    # API endpoints
    path('api/v1/auth/', include('apps.accounts.urls')),
    path('api/v1/customers/', include('apps.customers.urls')),
    path('api/v1/locations/', include('apps.locations.urls')),
    path('api/v1/currencies/', include('apps.currencies.urls')),
    path('api/v1/transactions/', include('apps.transactions.urls')),
    path('api/v1/reports/', include('apps.reports.urls')),
    path('api/v1/core/', include('apps.core.urls')),
    
    # Web interface
    path('', include('apps.core.web_urls')),
    path('accounts/', include('apps.accounts.web_urls')),
    path('customers/', include('apps.customers.web_urls')),
    path('transactions/', include('apps.transactions.web_urls')),
    path('reports/', include('apps.reports.web_urls')),
    
    # Redirect root to dashboard
    path('', RedirectView.as_view(url='/dashboard/', permanent=False)),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # Add debug toolbar if available
    try:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
        logger.debug("Debug toolbar URLs added")
    except ImportError:
        logger.debug("Debug toolbar not available")

# Custom admin site configuration
admin.site.site_header = "Arena Doviz Exchange Accounting"
admin.site.site_title = "Arena Doviz Admin"
admin.site.index_title = "Welcome to Arena Doviz Administration"

# Log URL configuration loading
logger.info("Arena Doviz URL configuration loaded successfully")
