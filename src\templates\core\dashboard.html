{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h2 mb-4">
            <i class="bi bi-speedometer2"></i>
            {% trans "Dashboard" %}
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Total Customers" %}</h5>
                        <h2 class="mb-0" id="total-customers">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Today's Transactions" %}</h5>
                        <h2 class="mb-0" id="today-transactions">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Active Locations" %}</h5>
                        <h2 class="mb-0" id="active-locations">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-geo-alt fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Pending Approvals" %}</h5>
                        <h2 class="mb-0" id="pending-approvals">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Daily Transaction Volume" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    {% trans "Currency Distribution" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="currencyChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Recent Transactions" %}
                </h5>
                <a href="{% url 'transactions_web:list' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exchange Rates -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "Current Exchange Rates" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="exchange-rates">
                    <div class="col-12 text-center text-muted">
                        {% trans "Loading exchange rates..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize dashboard
    loadDashboardData();
    initializeCharts();
    
    // Refresh data every 5 minutes
    setInterval(loadDashboardData, 300000);
});

function loadDashboardData() {
    // Load summary statistics
    loadSummaryStats();
    
    // Load recent transactions
    loadRecentTransactions();
    
    // Load exchange rates
    loadExchangeRates();
}

function loadSummaryStats() {
    // This would typically make AJAX calls to API endpoints
    // For now, we'll use placeholder data
    $('#total-customers').text('150');
    $('#today-transactions').text('23');
    $('#active-locations').text('5');
    $('#pending-approvals').text('3');
}

function loadRecentTransactions() {
    // Placeholder data for recent transactions
    const transactions = [
        {
            number: '20250811-0001',
            customer: 'John Doe',
            amount: '$1,000 → ﷼42,000,000',
            status: 'Completed',
            date: '2025-08-11 14:30',
            id: '1'
        },
        {
            number: '20250811-0002',
            customer: 'ABC Company',
            amount: 'د.إ5,000 → $1,361',
            status: 'Pending',
            date: '2025-08-11 15:15',
            id: '2'
        }
    ];
    
    const tbody = $('#recent-transactions-table tbody');
    tbody.empty();
    
    transactions.forEach(function(tx) {
        const statusClass = tx.status === 'Completed' ? 'success' : 'warning';
        const row = `
            <tr>
                <td><code>${tx.number}</code></td>
                <td>${tx.customer}</td>
                <td>${tx.amount}</td>
                <td><span class="badge bg-${statusClass}">${tx.status}</span></td>
                <td>${tx.date}</td>
                <td>
                    <a href="/transactions/${tx.id}/" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-eye"></i>
                    </a>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function loadExchangeRates() {
    // Placeholder exchange rates
    const rates = [
        { from: 'USD', to: 'IRR', buy: '42,000', sell: '42,500' },
        { from: 'AED', to: 'IRR', buy: '11,450', sell: '11,580' },
        { from: 'USD', to: 'AED', buy: '3.67', sell: '3.68' }
    ];
    
    const container = $('#exchange-rates');
    container.empty();
    
    rates.forEach(function(rate) {
        const card = `
            <div class="col-md-4 mb-3">
                <div class="card border-primary">
                    <div class="card-body text-center">
                        <h6 class="card-title">${rate.from} → ${rate.to}</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Buy</small>
                                <div class="fw-bold text-success">${rate.buy}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Sell</small>
                                <div class="fw-bold text-danger">${rate.sell}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.append(card);
    });
}

function initializeCharts() {
    // Transaction Volume Chart
    const txCtx = document.getElementById('transactionChart').getContext('2d');
    new Chart(txCtx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Transaction Volume',
                data: [12, 19, 3, 5, 2, 3, 15],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Currency Distribution Chart
    const currCtx = document.getElementById('currencyChart').getContext('2d');
    new Chart(currCtx, {
        type: 'doughnut',
        data: {
            labels: ['USD', 'AED', 'IRR'],
            datasets: [{
                data: [45, 30, 25],
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}
</script>
{% endblock %}
