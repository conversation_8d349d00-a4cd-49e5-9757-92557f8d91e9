# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

## [0.1.0] - 2025-08-11
- Docker-based dev stack scaffolded under deployment/ with docker-compose, Dockerfile, Nginx config, requirements; .env.example added

### Added
- Deployment Guide (docs/technical/deployment_guide.md) targeting Windows Server + Nginx + Gunicorn + Docker
- Configuration Guide (docs/technical/configuration_guide.md) covering ENV, security, roles, per-location rates
- Troubleshooting Guide (docs/technical/troubleshooting_guide.md)

### Changed
- Updated docs/technical/module_specifications.md to reflect Arena Doviz business rules:
  - Access limited to employees/managers; no direct access for customers/couriers
  - Roles adjusted: Admin, Accountant/Branch Employee, Viewer; Courier as operational role without login
  - WhatsApp Desktop notification flow (preview/approve/send)
  - Expanded transaction types and commission handling, multi-step payments, attachments
  - Exchange rates per location with endpoints
  - Balance traceability (drill-down) and alerts
  - Reporting columns and profit per transaction/daily profit
  - Added multilingual section (XML-based)
- Updated docs/business/business_requirements.md to include:
  - Per-location latest buy/sell display
  - WhatsApp Desktop flow note
  - Attachment requirement in multi-step transactions

### Notes
- No code changes yet; documentation aligned with Statement Of Account expectations and new business notes.

