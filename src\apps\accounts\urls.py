"""
API URL configuration for Arena Doviz Accounts app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

app_name = 'accounts'

# API router for accounts endpoints
router = DefaultRouter()

urlpatterns = [
    path('', include(router.urls)),
    path('login/', lambda request: None, name='api_login'),
    path('logout/', lambda request: None, name='api_logout'),
    path('profile/', lambda request: None, name='api_profile'),
]
